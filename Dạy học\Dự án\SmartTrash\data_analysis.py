#!/usr/bin/env python3
"""
Script phân tích dataset SmartTrash
Đếm số lượng <PERSON>nh, kiểm tra kích thước, tạo visualization
"""

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict
import pandas as pd
from PIL import Image
import warnings
warnings.filterwarnings('ignore')

# Đường dẫn dataset
DATASET_PATH = "rubbish-data"
CLASSES = ['battery', 'biological', 'brown-glass', 'cardboard', 'green-glass', 
           'metal', 'paper', 'plastic', 'trash', 'white-glass']

def count_images_per_class():
    """Đếm số lượng ảnh trong mỗi class và split"""
    data = defaultdict(dict)
    
    for split in ['train', 'val', 'test']:
        split_path = os.path.join(DATASET_PATH, split)
        if not os.path.exists(split_path):
            continue
            
        for class_name in CLASSES:
            class_path = os.path.join(split_path, class_name)
            if os.path.exists(class_path):
                count = len([f for f in os.listdir(class_path) 
                           if f.lower().endswith(('.jpg', '.jpeg', '.png'))])
                data[class_name][split] = count
            else:
                data[class_name][split] = 0
    
    return data

def analyze_image_properties():
    """Phân tích kích thước và properties của ảnh"""
    image_info = []
    
    for split in ['train', 'val', 'test']:
        split_path = os.path.join(DATASET_PATH, split)
        if not os.path.exists(split_path):
            continue
            
        for class_name in CLASSES:
            class_path = os.path.join(split_path, class_name)
            if not os.path.exists(class_path):
                continue
                
            # Lấy sample 10 ảnh từ mỗi class để phân tích
            images = [f for f in os.listdir(class_path) 
                     if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
            sample_images = images[:min(10, len(images))]
            
            for img_name in sample_images:
                img_path = os.path.join(class_path, img_name)
                try:
                    # Sử dụng PIL để đọc ảnh
                    with Image.open(img_path) as img:
                        width, height = img.size
                        mode = img.mode
                        
                    # Tính file size
                    file_size = os.path.getsize(img_path) / 1024  # KB
                    
                    image_info.append({
                        'class': class_name,
                        'split': split,
                        'width': width,
                        'height': height,
                        'mode': mode,
                        'file_size_kb': file_size,
                        'aspect_ratio': width / height
                    })
                except Exception as e:
                    print(f"Lỗi đọc ảnh {img_path}: {e}")
    
    return pd.DataFrame(image_info)

def create_visualizations(count_data, image_df):
    """Tạo các biểu đồ visualization"""
    plt.style.use('seaborn-v0_8')
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('SmartTrash Dataset Analysis', fontsize=16, fontweight='bold')
    
    # 1. Biểu đồ số lượng ảnh theo class
    df_count = pd.DataFrame(count_data).T.fillna(0)
    df_count.plot(kind='bar', ax=axes[0,0], color=['#FF6B6B', '#4ECDC4', '#45B7D1'])
    axes[0,0].set_title('Số lượng ảnh theo Class và Split')
    axes[0,0].set_xlabel('Classes')
    axes[0,0].set_ylabel('Số lượng ảnh')
    axes[0,0].tick_params(axis='x', rotation=45)
    axes[0,0].legend()
    
    # 2. Phân bố kích thước ảnh
    if not image_df.empty:
        axes[0,1].scatter(image_df['width'], image_df['height'], 
                         c=image_df['class'].astype('category').cat.codes, 
                         alpha=0.6, cmap='tab10')
        axes[0,1].set_title('Phân bố kích thước ảnh (Width vs Height)')
        axes[0,1].set_xlabel('Width (pixels)')
        axes[0,1].set_ylabel('Height (pixels)')
        
        # 3. Histogram aspect ratio
        image_df['aspect_ratio'].hist(bins=20, ax=axes[1,0], color='skyblue', alpha=0.7)
        axes[1,0].set_title('Phân bố Aspect Ratio')
        axes[1,0].set_xlabel('Aspect Ratio (Width/Height)')
        axes[1,0].set_ylabel('Frequency')
        
        # 4. File size distribution
        image_df['file_size_kb'].hist(bins=20, ax=axes[1,1], color='lightcoral', alpha=0.7)
        axes[1,1].set_title('Phân bố File Size')
        axes[1,1].set_xlabel('File Size (KB)')
        axes[1,1].set_ylabel('Frequency')
    
    plt.tight_layout()
    plt.savefig('dataset_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

def print_summary(count_data, image_df):
    """In tóm tắt thông tin dataset"""
    print("="*60)
    print("📊 SMARTTRASH DATASET ANALYSIS SUMMARY")
    print("="*60)
    
    # Tổng số ảnh
    df_count = pd.DataFrame(count_data).T.fillna(0)
    total_train = df_count['train'].sum()
    total_val = df_count['val'].sum()
    total_test = df_count['test'].sum()
    total_all = total_train + total_val + total_test
    
    print(f"📈 TỔNG QUAN:")
    print(f"   • Tổng số classes: {len(CLASSES)}")
    print(f"   • Tổng số ảnh: {int(total_all):,}")
    print(f"   • Train: {int(total_train):,} ảnh ({total_train/total_all*100:.1f}%)")
    print(f"   • Validation: {int(total_val):,} ảnh ({total_val/total_all*100:.1f}%)")
    print(f"   • Test: {int(total_test):,} ảnh ({total_test/total_all*100:.1f}%)")
    
    print(f"\n📋 CHI TIẾT THEO CLASS:")
    for class_name in CLASSES:
        train_count = count_data[class_name].get('train', 0)
        val_count = count_data[class_name].get('val', 0)
        test_count = count_data[class_name].get('test', 0)
        total_class = train_count + val_count + test_count
        print(f"   • {class_name:12}: {total_class:4d} ảnh (Train: {train_count:3d}, Val: {val_count:2d}, Test: {test_count:2d})")
    
    if not image_df.empty:
        print(f"\n🖼️  THÔNG TIN ẢNH (Sample Analysis):")
        print(f"   • Kích thước trung bình: {image_df['width'].mean():.0f} x {image_df['height'].mean():.0f} pixels")
        print(f"   • Kích thước min: {image_df['width'].min():.0f} x {image_df['height'].min():.0f} pixels")
        print(f"   • Kích thước max: {image_df['width'].max():.0f} x {image_df['height'].max():.0f} pixels")
        print(f"   • File size trung bình: {image_df['file_size_kb'].mean():.1f} KB")
        print(f"   • Aspect ratio trung bình: {image_df['aspect_ratio'].mean():.2f}")
        
        # Kiểm tra color mode
        color_modes = image_df['mode'].value_counts()
        print(f"   • Color modes: {dict(color_modes)}")
    
    print(f"\n💡 KHUYẾN NGHỊ:")
    print(f"   • Dataset cân bằng tốt với {len(CLASSES)} classes")
    print(f"   • Nên resize ảnh về kích thước chuẩn (224x224 hoặc 256x256)")
    print(f"   • Có thể áp dụng data augmentation để tăng cường dataset")
    print(f"   • Sử dụng Transfer Learning với MobileNet hoặc ResNet")

def main():
    """Hàm main chạy phân tích"""
    print("🔍 Bắt đầu phân tích dataset SmartTrash...")
    
    # Kiểm tra dataset path
    if not os.path.exists(DATASET_PATH):
        print(f"❌ Không tìm thấy dataset tại: {DATASET_PATH}")
        return
    
    # Đếm số lượng ảnh
    print("📊 Đang đếm số lượng ảnh...")
    count_data = count_images_per_class()
    
    # Phân tích properties ảnh
    print("🖼️  Đang phân tích properties ảnh...")
    image_df = analyze_image_properties()
    
    # Tạo visualization
    print("📈 Đang tạo biểu đồ...")
    create_visualizations(count_data, image_df)
    
    # In summary
    print_summary(count_data, image_df)
    
    print("\n✅ Hoàn thành phân tích dataset!")
    print("📁 Biểu đồ đã được lưu: dataset_analysis.png")

if __name__ == "__main__":
    main()

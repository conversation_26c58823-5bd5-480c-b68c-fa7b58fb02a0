# 🗑️ SmartTrash Model Training Guide

## 📋 Tổng quan
Hướng dẫn training model phân loại rác thông minh sử dụng Google Colab với Transfer Learning.

## 📊 Dataset Information
- **Tổng số ảnh**: 10,138 images
- **Số classes**: 10 loại rác
- **Phân chia**: Train (78.7%) | Val (10.4%) | Test (11.0%)

### 🗂️ Classes
1. **battery** - Pin/ắc quy
2. **biological** - <PERSON><PERSON><PERSON> h<PERSON> c<PERSON>  
3. **brown-glass** - Thủy tinh nâu
4. **cardboard** - Bìa carton
5. **green-glass** - Thủy tinh xanh
6. **metal** - <PERSON>
7. **paper** - Giấy
8. **plastic** - Nhựa
9. **trash** - R<PERSON><PERSON> thông thường
10. **white-glass** - Thủy tinh trắng

## 🚀 Hướng dẫn Training trên Google Colab

### Bước 1: Chuẩn bị Dataset
1. Upload folder `rubbish-data` lên Google Drive
2. <PERSON><PERSON><PERSON> b<PERSON>o cấu trúc folder như sau:
```
rubbish-data/
├── train/
│   ├── battery/
│   ├── biological/
│   ├── brown-glass/
│   └── ... (10 classes)
├── val/
│   └── ... (10 classes)
└── test/
    └── ... (10 classes)
```

### Bước 2: Mở Google Colab
1. Truy cập [Google Colab](https://colab.research.google.com/)
2. Upload file `SmartTrash_Training.ipynb`
3. Chọn Runtime > Change runtime type > GPU (T4 hoặc V100)

### Bước 3: Chạy Training
1. Chạy từng cell theo thứ tự
2. Mount Google Drive khi được yêu cầu
3. Điều chỉnh đường dẫn dataset nếu cần:
```python
DATASET_PATH = '/content/drive/MyDrive/SmartTrash/rubbish-data'
```

### Bước 4: Monitor Training
- Training sẽ chạy tối đa 50 epochs
- EarlyStopping sẽ dừng khi không cải thiện
- Model tốt nhất sẽ được lưu tự động

### Bước 5: Download Model
- Model package sẽ được tạo và download tự động
- Bao gồm: model.h5, labels.txt, model_info.json

## ⚙️ Model Architecture
- **Base Model**: MobileNetV2 (pre-trained on ImageNet)
- **Custom Head**: GlobalAveragePooling2D + Dense layers
- **Input Size**: 224x224x3
- **Output**: 10 classes (softmax)

## 📈 Expected Performance
- **Target Accuracy**: >90% trên test set
- **Training Time**: 30-60 phút (với GPU T4)
- **Model Size**: ~10MB

## 🔧 Hyperparameters
```python
IMG_SIZE = 224
BATCH_SIZE = 32
LEARNING_RATE = 0.001
EPOCHS = 50
OPTIMIZER = Adam
```

## 📁 Output Files
1. **smarttrash_model.h5** - Model chính để sử dụng
2. **labels.txt** - Danh sách 10 classes
3. **model_info.json** - Metadata và thông tin model
4. **training_history.png** - Biểu đồ quá trình training
5. **confusion_matrix.png** - Ma trận nhầm lẫn

## 🚨 Troubleshooting

### Lỗi thường gặp:
1. **Out of Memory**: Giảm BATCH_SIZE xuống 16 hoặc 8
2. **Dataset not found**: Kiểm tra đường dẫn Google Drive
3. **GPU not available**: Chọn Runtime > Change runtime type > GPU

### Tips tối ưu:
- Sử dụng GPU T4 hoặc cao hơn
- Đảm bảo có đủ dung lượng Google Drive (>2GB)
- Kiểm tra kết nối internet ổn định

## 📞 Support
Nếu gặp vấn đề, kiểm tra:
1. Cấu trúc dataset đúng format
2. Đường dẫn Google Drive chính xác
3. Runtime đã chọn GPU
4. Đủ dung lượng lưu trữ

## 🎯 Next Steps
Sau khi training xong:
1. Download model package về local
2. Tích hợp vào web application
3. Test với ảnh thực tế
4. Deploy lên production

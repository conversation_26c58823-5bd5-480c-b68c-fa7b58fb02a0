#!/usr/bin/env python3
"""
Script test model SmartTrash sau khi training
Kiểm tra model có hoạt động đúng không
"""

import tensorflow as tf
import numpy as np
import cv2
from PIL import Image
import json
import os
import matplotlib.pyplot as plt

class SmartTrashPredictor:
    def __init__(self, model_path, labels_path, model_info_path=None):
        """
        Initialize SmartTrash predictor
        
        Args:
            model_path: Đường dẫn đến file model.h5
            labels_path: Đường dẫn đến file labels.txt
            model_info_path: Đường dẫn đến file model_info.json (optional)
        """
        self.model = self.load_model(model_path)
        self.class_names = self.load_labels(labels_path)
        self.model_info = self.load_model_info(model_info_path) if model_info_path else None
        
        print(f"✅ Model loaded successfully!")
        print(f"📋 Classes: {len(self.class_names)}")
        print(f"🏷️  Labels: {self.class_names}")
    
    def load_model(self, model_path):
        """Load trained model"""
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"Model file not found: {model_path}")
        
        try:
            model = tf.keras.models.load_model(model_path)
            print(f"📦 Model loaded from: {model_path}")
            return model
        except Exception as e:
            raise Exception(f"Error loading model: {e}")
    
    def load_labels(self, labels_path):
        """Load class labels"""
        if not os.path.exists(labels_path):
            raise FileNotFoundError(f"Labels file not found: {labels_path}")
        
        with open(labels_path, 'r') as f:
            labels = [line.strip() for line in f.readlines()]
        
        print(f"🏷️  Labels loaded from: {labels_path}")
        return labels
    
    def load_model_info(self, info_path):
        """Load model information"""
        if not os.path.exists(info_path):
            print(f"⚠️  Model info file not found: {info_path}")
            return None
        
        with open(info_path, 'r') as f:
            info = json.load(f)
        
        print(f"ℹ️  Model info loaded from: {info_path}")
        return info
    
    def preprocess_image(self, image_path, target_size=(224, 224)):
        """
        Preprocess image for prediction
        
        Args:
            image_path: Đường dẫn đến ảnh
            target_size: Kích thước target (width, height)
        
        Returns:
            Preprocessed image array
        """
        try:
            # Load image
            img = Image.open(image_path)
            
            # Convert to RGB if needed
            if img.mode != 'RGB':
                img = img.convert('RGB')
            
            # Resize
            img = img.resize(target_size)
            
            # Convert to array and normalize
            img_array = np.array(img) / 255.0
            
            # Add batch dimension
            img_array = np.expand_dims(img_array, axis=0)
            
            return img_array
            
        except Exception as e:
            raise Exception(f"Error preprocessing image {image_path}: {e}")
    
    def predict(self, image_path, top_k=3):
        """
        Predict image class
        
        Args:
            image_path: Đường dẫn đến ảnh
            top_k: Số lượng predictions hàng đầu
        
        Returns:
            Dictionary với predictions
        """
        # Preprocess image
        img_array = self.preprocess_image(image_path)
        
        # Make prediction
        predictions = self.model.predict(img_array, verbose=0)
        
        # Get top-k predictions
        top_indices = np.argsort(predictions[0])[::-1][:top_k]
        
        results = {
            'image_path': image_path,
            'predictions': []
        }
        
        for i, idx in enumerate(top_indices):
            results['predictions'].append({
                'rank': i + 1,
                'class': self.class_names[idx],
                'confidence': float(predictions[0][idx]),
                'percentage': float(predictions[0][idx] * 100)
            })
        
        return results
    
    def predict_and_display(self, image_path, top_k=3):
        """Predict và hiển thị kết quả"""
        results = self.predict(image_path, top_k)
        
        # Display image
        img = Image.open(image_path)
        plt.figure(figsize=(10, 6))
        
        plt.subplot(1, 2, 1)
        plt.imshow(img)
        plt.title(f'Input Image\n{os.path.basename(image_path)}')
        plt.axis('off')
        
        # Display predictions
        plt.subplot(1, 2, 2)
        classes = [pred['class'] for pred in results['predictions']]
        confidences = [pred['confidence'] for pred in results['predictions']]
        
        y_pos = np.arange(len(classes))
        bars = plt.barh(y_pos, confidences, color=['#FF6B6B', '#4ECDC4', '#45B7D1'][:len(classes)])
        
        plt.yticks(y_pos, classes)
        plt.xlabel('Confidence')
        plt.title('Top Predictions')
        plt.xlim(0, 1)
        
        # Add percentage labels
        for i, (bar, pred) in enumerate(zip(bars, results['predictions'])):
            plt.text(bar.get_width() + 0.01, bar.get_y() + bar.get_height()/2, 
                    f'{pred["percentage"]:.1f}%', 
                    va='center', fontsize=10)
        
        plt.tight_layout()
        plt.show()
        
        # Print results
        print(f"\n🔮 PREDICTION RESULTS:")
        print(f"📁 Image: {os.path.basename(image_path)}")
        for pred in results['predictions']:
            print(f"   {pred['rank']}. {pred['class']:12} - {pred['percentage']:5.1f}% confidence")
        
        return results

def test_model_with_samples():
    """Test model với một số ảnh mẫu"""
    
    # Đường dẫn model (điều chỉnh theo thực tế)
    MODEL_PATH = "models/smarttrash_model.h5"
    LABELS_PATH = "models/labels.txt"
    MODEL_INFO_PATH = "models/model_info.json"
    
    # Kiểm tra files tồn tại
    if not os.path.exists(MODEL_PATH):
        print(f"❌ Model file not found: {MODEL_PATH}")
        print("💡 Hãy đảm bảo đã training và download model từ Colab")
        return
    
    if not os.path.exists(LABELS_PATH):
        print(f"❌ Labels file not found: {LABELS_PATH}")
        return
    
    # Initialize predictor
    try:
        predictor = SmartTrashPredictor(MODEL_PATH, LABELS_PATH, MODEL_INFO_PATH)
    except Exception as e:
        print(f"❌ Error initializing predictor: {e}")
        return
    
    # Test với ảnh mẫu từ test set
    test_images = []
    test_dir = "rubbish-data/test"
    
    if os.path.exists(test_dir):
        for class_name in predictor.class_names:
            class_dir = os.path.join(test_dir, class_name)
            if os.path.exists(class_dir):
                images = [f for f in os.listdir(class_dir) 
                         if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
                if images:
                    test_images.append(os.path.join(class_dir, images[0]))
    
    if not test_images:
        print("❌ No test images found!")
        print("💡 Hãy đảm bảo có folder rubbish-data/test với ảnh mẫu")
        return
    
    # Test với 3 ảnh đầu tiên
    print(f"\n🧪 Testing model với {min(3, len(test_images))} ảnh mẫu...")
    
    for i, image_path in enumerate(test_images[:3]):
        print(f"\n{'='*50}")
        print(f"TEST {i+1}: {image_path}")
        print('='*50)
        
        try:
            results = predictor.predict_and_display(image_path)
            
            # Kiểm tra accuracy
            true_class = os.path.basename(os.path.dirname(image_path))
            predicted_class = results['predictions'][0]['class']
            
            if true_class == predicted_class:
                print(f"✅ CORRECT! True: {true_class}, Predicted: {predicted_class}")
            else:
                print(f"❌ WRONG! True: {true_class}, Predicted: {predicted_class}")
                
        except Exception as e:
            print(f"❌ Error testing {image_path}: {e}")
    
    print(f"\n✅ Model testing completed!")

def main():
    """Main function"""
    print("🧪 SmartTrash Model Testing")
    print("="*40)
    
    test_model_with_samples()

if __name__ == "__main__":
    main()

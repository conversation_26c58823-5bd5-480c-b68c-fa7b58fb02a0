# 🚀 QUICK START - SmartTrash Training

## 📋 Hướng dẫn nhanh training model trên Google Colab

### 🎯 Mục tiêu
Training model phân loại rác với 10 classes trong 30-45 phút

### 📁 Chuẩn bị
1. **Upload dataset lên Google Drive**:
   ```
   MyDrive/SmartTrash/rubbish-data/
   ├── train/
   ├── val/
   └── test/
   ```

2. **Mở Google Colab**: [colab.research.google.com](https://colab.research.google.com/)

3. **Upload file**: `SmartTrash_Simple_Training.ipynb`

### ⚙️ Setup Colab
1. **Chọn GPU**: Runtime > Change runtime type > GPU (T4)
2. **Kiểm tra GPU**: 
   ```python
   import tensorflow as tf
   print(tf.config.list_physical_devices('GPU'))
   ```

### 🔧 Điều chỉnh đường dẫn
**QUAN TRỌNG**: Sửa đường dẫn trong cell 2:
```python
# Thay đổi đường dẫn này theo cấu trúc Drive của bạn
DATASET_PATH = '/content/drive/MyDrive/SmartTrash/rubbish-data'
```

### 🏃‍♂️ Chạy Training
1. **Chạy từng cell theo thứ tự** (Shift + Enter)
2. **Mount Drive** khi được yêu cầu
3. **Đợi training hoàn thành** (~30 phút)
4. **Download model package** tự động

### 📊 Expected Results
- **Training Time**: 30-45 phút với GPU T4
- **Expected Accuracy**: >85% (có thể đạt >90%)
- **Model Size**: ~10MB
- **Output Files**: 
  - `smarttrash_model.h5` - Model chính
  - `labels.txt` - Danh sách classes
  - `model_info.json` - Thông tin model
  - `training_history.png` - Biểu đồ training

### 🚨 Troubleshooting

#### ❌ "Dataset not found"
```python
# Kiểm tra đường dẫn
import os
print(os.listdir('/content/drive/MyDrive/SmartTrash/'))
```

#### ❌ "Out of Memory"
```python
# Giảm batch size
BATCH_SIZE = 16  # thay vì 32
```

#### ❌ "No GPU available"
- Runtime > Change runtime type > GPU
- Restart runtime nếu cần

### 📈 Monitoring Training
```
Epoch 1/30
249/249 [==============================] - 45s 181ms/step - loss: 1.2345 - accuracy: 0.6789 - val_loss: 0.9876 - val_accuracy: 0.7654
```

**Dấu hiệu tốt**:
- Loss giảm dần
- Accuracy tăng dần
- Val_accuracy > 80%

### 🎉 Sau khi Training
1. **Download model package** (tự động)
2. **Giải nén** file zip
3. **Copy** vào folder dự án local
4. **Test model** với `test_model.py`

### 📞 Support
**Nếu gặp lỗi**:
1. Restart runtime: Runtime > Restart runtime
2. Kiểm tra GPU: Runtime > Change runtime type
3. Kiểm tra đường dẫn dataset
4. Giảm batch size nếu out of memory

### 🔄 Next Steps
Sau khi có model:
1. Test model locally
2. Tạo web application
3. Tích hợp camera
4. Thêm audio feedback

---

## 📋 Checklist

- [ ] Upload dataset lên Google Drive
- [ ] Mở Colab với GPU
- [ ] Upload notebook
- [ ] Sửa đường dẫn dataset
- [ ] Chạy training
- [ ] Download model
- [ ] Test model locally

**🎯 Goal**: Model với accuracy >85% trong 45 phút!

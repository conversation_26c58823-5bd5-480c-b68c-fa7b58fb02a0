#!/usr/bin/env python3
"""
Script phân tích nhanh dataset SmartTrash
Chỉ đếm số lượng và thống kê cơ bản
"""

import os
from collections import defaultdict
from PIL import Image

# Đường dẫn dataset
DATASET_PATH = "rubbish-data"
CLASSES = ['battery', 'biological', 'brown-glass', 'cardboard', 'green-glass', 
           'metal', 'paper', 'plastic', 'trash', 'white-glass']

def count_images_per_class():
    """Đếm số lượng ảnh trong mỗi class và split"""
    data = defaultdict(dict)
    
    for split in ['train', 'val', 'test']:
        split_path = os.path.join(DATASET_PATH, split)
        if not os.path.exists(split_path):
            continue
            
        for class_name in CLASSES:
            class_path = os.path.join(split_path, class_name)
            if os.path.exists(class_path):
                count = len([f for f in os.listdir(class_path) 
                           if f.lower().endswith(('.jpg', '.jpeg', '.png'))])
                data[class_name][split] = count
            else:
                data[class_name][split] = 0
    
    return data

def analyze_sample_images():
    """Phân tích sample ảnh để lấy thông tin kích thước"""
    image_info = []
    
    # Chỉ lấy 2 ảnh từ mỗi class trong train để phân tích nhanh
    train_path = os.path.join(DATASET_PATH, 'train')
    if not os.path.exists(train_path):
        return []
        
    for class_name in CLASSES:
        class_path = os.path.join(train_path, class_name)
        if not os.path.exists(class_path):
            continue
            
        images = [f for f in os.listdir(class_path) 
                 if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
        sample_images = images[:2]  # Chỉ lấy 2 ảnh
        
        for img_name in sample_images:
            img_path = os.path.join(class_path, img_name)
            try:
                with Image.open(img_path) as img:
                    width, height = img.size
                    
                file_size = os.path.getsize(img_path) / 1024  # KB
                
                image_info.append({
                    'class': class_name,
                    'width': width,
                    'height': height,
                    'file_size_kb': file_size,
                    'aspect_ratio': width / height
                })
            except Exception as e:
                print(f"Lỗi đọc ảnh {img_path}: {e}")
    
    return image_info

def print_summary(count_data, image_info):
    """In tóm tắt thông tin dataset"""
    print("="*60)
    print("📊 SMARTTRASH DATASET ANALYSIS SUMMARY")
    print("="*60)
    
    # Tổng số ảnh
    total_train = sum(count_data[cls].get('train', 0) for cls in CLASSES)
    total_val = sum(count_data[cls].get('val', 0) for cls in CLASSES)
    total_test = sum(count_data[cls].get('test', 0) for cls in CLASSES)
    total_all = total_train + total_val + total_test
    
    print(f"📈 TỔNG QUAN:")
    print(f"   • Tổng số classes: {len(CLASSES)}")
    print(f"   • Tổng số ảnh: {int(total_all):,}")
    print(f"   • Train: {int(total_train):,} ảnh ({total_train/total_all*100:.1f}%)")
    print(f"   • Validation: {int(total_val):,} ảnh ({total_val/total_all*100:.1f}%)")
    print(f"   • Test: {int(total_test):,} ảnh ({total_test/total_all*100:.1f}%)")
    
    print(f"\n📋 CHI TIẾT THEO CLASS:")
    for class_name in CLASSES:
        train_count = count_data[class_name].get('train', 0)
        val_count = count_data[class_name].get('val', 0)
        test_count = count_data[class_name].get('test', 0)
        total_class = train_count + val_count + test_count
        print(f"   • {class_name:12}: {total_class:4d} ảnh (Train: {train_count:3d}, Val: {val_count:2d}, Test: {test_count:2d})")
    
    if image_info:
        widths = [img['width'] for img in image_info]
        heights = [img['height'] for img in image_info]
        file_sizes = [img['file_size_kb'] for img in image_info]
        aspect_ratios = [img['aspect_ratio'] for img in image_info]
        
        print(f"\n🖼️  THÔNG TIN ẢNH (Sample từ {len(image_info)} ảnh):")
        print(f"   • Kích thước trung bình: {sum(widths)/len(widths):.0f} x {sum(heights)/len(heights):.0f} pixels")
        print(f"   • Kích thước min: {min(widths):.0f} x {min(heights):.0f} pixels")
        print(f"   • Kích thước max: {max(widths):.0f} x {max(heights):.0f} pixels")
        print(f"   • File size trung bình: {sum(file_sizes)/len(file_sizes):.1f} KB")
        print(f"   • Aspect ratio trung bình: {sum(aspect_ratios)/len(aspect_ratios):.2f}")
    
    print(f"\n💡 KHUYẾN NGHỊ CHO TRAINING:")
    print(f"   • Dataset cân bằng tốt với {len(CLASSES)} classes")
    print(f"   • Nên resize ảnh về 224x224 pixels (chuẩn cho Transfer Learning)")
    print(f"   • Áp dụng data augmentation: rotation, flip, zoom, brightness")
    print(f"   • Sử dụng Transfer Learning với MobileNetV2 hoặc EfficientNet")
    print(f"   • Batch size khuyến nghị: 32-64")
    print(f"   • Learning rate ban đầu: 0.001")

def main():
    """Hàm main chạy phân tích"""
    print("🔍 Bắt đầu phân tích nhanh dataset SmartTrash...")
    
    # Kiểm tra dataset path
    if not os.path.exists(DATASET_PATH):
        print(f"❌ Không tìm thấy dataset tại: {DATASET_PATH}")
        return
    
    # Đếm số lượng ảnh
    print("📊 Đang đếm số lượng ảnh...")
    count_data = count_images_per_class()
    
    # Phân tích sample ảnh
    print("🖼️  Đang phân tích sample ảnh...")
    image_info = analyze_sample_images()
    
    # In summary
    print_summary(count_data, image_info)
    
    print("\n✅ Hoàn thành phân tích dataset!")

if __name__ == "__main__":
    main()

# Import libraries (sử dụng packages có sẵn trong Colab)
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
from tensorflow.keras.applications import MobileNetV2
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from tensorflow.keras.optimizers import <PERSON>
from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint, ReduceLROnPlateau

import numpy as np
import matplotlib.pyplot as plt
import os
import json
from google.colab import drive, files
import zipfile

# Set seeds
np.random.seed(42)
tf.random.set_seed(42)

print(f"✅ TensorFlow version: {tf.__version__}")
print(f"✅ GPU available: {len(tf.config.list_physical_devices('GPU')) > 0}")

# Mount Google Drive
drive.mount('/content/drive')

# ⚠️ QUAN TRỌNG: Điều chỉnh đường dẫn này theo cấu trúc Drive của bạn
BASE_PATH = '/content/drive/MyDrive/SmartTrash'  # Thay đổi đường dẫn này
DATASET_PATH = os.path.join(BASE_PATH, 'rubbish-data')
MODEL_SAVE_PATH = os.path.join(BASE_PATH, 'models')
ZIP_PATH = os.path.join(BASE_PATH, 'rubbish-data.zip')  # Đường dẫn file zip

# Tạo thư mục models
os.makedirs(MODEL_SAVE_PATH, exist_ok=True)

# Class names
CLASS_NAMES = ['battery', 'biological', 'brown-glass', 'cardboard', 'green-glass', 
               'metal', 'paper', 'plastic', 'trash', 'white-glass']

print(f"📁 Base path: {BASE_PATH}")
print(f"📁 Dataset path: {DATASET_PATH}")
print(f"💾 Model save path: {MODEL_SAVE_PATH}")
print(f"🏷️ Classes: {len(CLASS_NAMES)}")

# Kiểm tra và giải nén dataset nếu cần
if not os.path.exists(DATASET_PATH):
    print("\n📦 Dataset folder not found. Checking for zip file...")
    
    if os.path.exists(ZIP_PATH):
        print(f"✅ Found zip file: {ZIP_PATH}")
        print("🔄 Extracting dataset...")
        
        import zipfile
        with zipfile.ZipFile(ZIP_PATH, 'r') as zip_ref:
            zip_ref.extractall(BASE_PATH)
        
        print("✅ Dataset extracted successfully!")
    else:
        print(f"❌ Zip file not found: {ZIP_PATH}")
        print("💡 Please upload rubbish-data.zip to your Drive folder")
        print("💡 Or create rubbish-data folder with train/val/test subfolders")

# Kiểm tra dataset sau khi giải nén
if os.path.exists(DATASET_PATH):
    print("\n✅ Dataset found!")
    total_images = 0
    for split in ['train', 'val', 'test']:
        split_path = os.path.join(DATASET_PATH, split)
        if os.path.exists(split_path):
            count = sum([len(os.listdir(os.path.join(split_path, cls))) 
                        for cls in CLASS_NAMES if os.path.exists(os.path.join(split_path, cls))])
            total_images += count
            print(f"   📊 {split}: {count} images")
    print(f"   🎯 Total: {total_images} images")
    
    if total_images > 0:
        print("\n🚀 Ready for training!")
    else:
        print("\n⚠️ No images found in dataset folders")
else:
    print("\n❌ Dataset still not found after extraction attempt")
    print("\n🔍 Available files in base directory:")
    if os.path.exists(BASE_PATH):
        for item in os.listdir(BASE_PATH):
            print(f"   - {item}")
    else:
        print(f"   Base path doesn't exist: {BASE_PATH}")

# Cấu hình
IMG_SIZE = 224
BATCH_SIZE = 32
NUM_CLASSES = len(CLASS_NAMES)

# Data generators
train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=20,
    width_shift_range=0.2,
    height_shift_range=0.2,
    shear_range=0.2,
    zoom_range=0.2,
    horizontal_flip=True,
    fill_mode='nearest'
)

val_test_datagen = ImageDataGenerator(rescale=1./255)

# Create generators
train_generator = train_datagen.flow_from_directory(
    os.path.join(DATASET_PATH, 'train'),
    target_size=(IMG_SIZE, IMG_SIZE),
    batch_size=BATCH_SIZE,
    class_mode='categorical',
    classes=CLASS_NAMES,
    shuffle=True
)

val_generator = val_test_datagen.flow_from_directory(
    os.path.join(DATASET_PATH, 'val'),
    target_size=(IMG_SIZE, IMG_SIZE),
    batch_size=BATCH_SIZE,
    class_mode='categorical',
    classes=CLASS_NAMES,
    shuffle=False
)

test_generator = val_test_datagen.flow_from_directory(
    os.path.join(DATASET_PATH, 'test'),
    target_size=(IMG_SIZE, IMG_SIZE),
    batch_size=BATCH_SIZE,
    class_mode='categorical',
    classes=CLASS_NAMES,
    shuffle=False
)

print(f"✅ Training samples: {train_generator.samples}")
print(f"✅ Validation samples: {val_generator.samples}")
print(f"✅ Test samples: {test_generator.samples}")

# Tạo model với MobileNetV2
def create_model():
    base_model = MobileNetV2(
        weights='imagenet',
        include_top=False,
        input_shape=(IMG_SIZE, IMG_SIZE, 3)
    )
    
    # Freeze base model
    base_model.trainable = False
    
    # Add custom head
    model = keras.Sequential([
        base_model,
        layers.GlobalAveragePooling2D(),
        layers.Dropout(0.3),
        layers.Dense(512, activation='relu'),
        layers.BatchNormalization(),
        layers.Dropout(0.5),
        layers.Dense(NUM_CLASSES, activation='softmax')
    ])
    
    return model

# Create and compile model
model = create_model()
model.compile(
    optimizer=Adam(learning_rate=0.001),
    loss='categorical_crossentropy',
    metrics=['accuracy']
)

print("✅ Model created and compiled!")
print(f"📊 Total parameters: {model.count_params():,}")

# Setup callbacks
callbacks = [
    EarlyStopping(
        monitor='val_accuracy',
        patience=10,
        restore_best_weights=True,
        verbose=1
    ),
    ModelCheckpoint(
        filepath=os.path.join(MODEL_SAVE_PATH, 'best_model.h5'),
        monitor='val_accuracy',
        save_best_only=True,
        verbose=1
    ),
    ReduceLROnPlateau(
        monitor='val_loss',
        factor=0.5,
        patience=5,
        min_lr=1e-7,
        verbose=1
    )
]

# Training parameters
EPOCHS = 30  # Giảm epochs để training nhanh hơn
steps_per_epoch = train_generator.samples // BATCH_SIZE
validation_steps = val_generator.samples // BATCH_SIZE

print(f"🚀 Starting training for {EPOCHS} epochs...")
print(f"📊 Steps per epoch: {steps_per_epoch}")
print(f"📊 Validation steps: {validation_steps}")

# Train model
history = model.fit(
    train_generator,
    steps_per_epoch=steps_per_epoch,
    epochs=EPOCHS,
    validation_data=val_generator,
    validation_steps=validation_steps,
    callbacks=callbacks,
    verbose=1
)

print("\n🎉 Training completed!")

# Evaluate model
print("📊 Evaluating model on test set...")
test_loss, test_accuracy = model.evaluate(test_generator, verbose=1)

print(f"\n🎯 TEST RESULTS:")
print(f"   • Test Loss: {test_loss:.4f}")
print(f"   • Test Accuracy: {test_accuracy:.4f} ({test_accuracy*100:.2f}%)")

# Plot training history
plt.figure(figsize=(12, 4))

plt.subplot(1, 2, 1)
plt.plot(history.history['accuracy'], label='Training Accuracy')
plt.plot(history.history['val_accuracy'], label='Validation Accuracy')
plt.title('Model Accuracy')
plt.xlabel('Epoch')
plt.ylabel('Accuracy')
plt.legend()
plt.grid(True)

plt.subplot(1, 2, 2)
plt.plot(history.history['loss'], label='Training Loss')
plt.plot(history.history['val_loss'], label='Validation Loss')
plt.title('Model Loss')
plt.xlabel('Epoch')
plt.ylabel('Loss')
plt.legend()
plt.grid(True)

plt.tight_layout()
plt.savefig(os.path.join(MODEL_SAVE_PATH, 'training_history.png'), dpi=150, bbox_inches='tight')
plt.show()

# Save model và files
print("💾 Saving model and files...")

# Save model
model_path = os.path.join(MODEL_SAVE_PATH, 'smarttrash_model.h5')
model.save(model_path)
print(f"✅ Model saved: {model_path}")

# Save labels
labels_path = os.path.join(MODEL_SAVE_PATH, 'labels.txt')
with open(labels_path, 'w') as f:
    for label in CLASS_NAMES:
        f.write(f"{label}\n")
print(f"✅ Labels saved: {labels_path}")

# Save model info
model_info = {
    'model_name': 'SmartTrash Classifier',
    'architecture': 'MobileNetV2 + Custom Head',
    'input_size': [IMG_SIZE, IMG_SIZE, 3],
    'num_classes': NUM_CLASSES,
    'class_names': CLASS_NAMES,
    'test_accuracy': float(test_accuracy),
    'preprocessing': {
        'rescale': '1./255',
        'target_size': [IMG_SIZE, IMG_SIZE]
    },
    'training_info': {
        'batch_size': BATCH_SIZE,
        'epochs_trained': len(history.history['accuracy']),
        'optimizer': 'Adam',
        'loss': 'categorical_crossentropy'
    }
}

info_path = os.path.join(MODEL_SAVE_PATH, 'model_info.json')
with open(info_path, 'w') as f:
    json.dump(model_info, f, indent=2)
print(f"✅ Model info saved: {info_path}")

print("\n🎉 All files saved successfully!")

# Tạo và download model package
print("📦 Creating model package for download...")

zip_path = os.path.join(MODEL_SAVE_PATH, 'smarttrash_model_package.zip')
with zipfile.ZipFile(zip_path, 'w') as zipf:
    # Add model files
    zipf.write(model_path, 'smarttrash_model.h5')
    zipf.write(labels_path, 'labels.txt')
    zipf.write(info_path, 'model_info.json')
    
    # Add training plot if exists
    plot_path = os.path.join(MODEL_SAVE_PATH, 'training_history.png')
    if os.path.exists(plot_path):
        zipf.write(plot_path, 'training_history.png')

print(f"✅ Package created: {zip_path}")

# Download
files.download(zip_path)
print("📥 Model package downloaded!")

print("\n🎯 TRAINING SUMMARY:")
print(f"   • Model: MobileNetV2 Transfer Learning")
print(f"   • Classes: {NUM_CLASSES}")
print(f"   • Test Accuracy: {test_accuracy*100:.2f}%")
print(f"   • Epochs Trained: {len(history.history['accuracy'])}")
print(f"   • Files: smarttrash_model.h5, labels.txt, model_info.json")
print("\n🚀 Ready for web application integration!")
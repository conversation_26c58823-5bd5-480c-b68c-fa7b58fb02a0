#!/usr/bin/env python3
"""
SmartTrash Web Application
Flask app chính cho hệ thống phân loại rác thông minh
"""

from flask import Flask, render_template, request, jsonify, send_from_directory
import os
import cv2
import numpy as np
from PIL import Image
import tensorflow as tf
import json
from datetime import datetime
import uuid
from werkzeug.utils import secure_filename
import base64
import io

# Import custom utilities
from utils.model_loader import SmartTrashModel
from utils.image_processor import ImageProcessor
from utils.audio_generator import AudioGenerator

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'smarttrash_secret_key_2024'
app.config['UPLOAD_FOLDER'] = 'static/uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Allowed file extensions
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp'}

# Global variables
model = None
image_processor = None
audio_generator = None

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def init_app():
    """Initialize application components"""
    global model, image_processor, audio_generator
    
    try:
        # Initialize model
        model_path = '../models/smarttrash_model.h5'
        labels_path = '../models/labels.txt'
        model_info_path = '../models/model_info.json'
        
        print("🔄 Loading SmartTrash model...")
        model = SmartTrashModel(model_path, labels_path, model_info_path)
        print("✅ Model loaded successfully!")
        
        # Initialize image processor
        image_processor = ImageProcessor()
        print("✅ Image processor initialized!")
        
        # Initialize audio generator
        audio_generator = AudioGenerator()
        print("✅ Audio generator initialized!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error initializing app: {e}")
        return False

@app.route('/')
def index():
    """Main page"""
    return render_template('index.html')

@app.route('/camera')
def camera():
    """Camera page for real-time detection"""
    return render_template('camera.html')

@app.route('/upload')
def upload_page():
    """Upload page for file upload"""
    return render_template('upload.html')

@app.route('/api/predict', methods=['POST'])
def predict():
    """API endpoint for image prediction"""
    try:
        if model is None:
            return jsonify({'error': 'Model not loaded'}), 500
        
        # Check if image data is provided
        if 'image' not in request.files and 'image_data' not in request.json:
            return jsonify({'error': 'No image provided'}), 400
        
        # Handle file upload
        if 'image' in request.files:
            file = request.files['image']
            if file.filename == '':
                return jsonify({'error': 'No file selected'}), 400
            
            if file and allowed_file(file.filename):
                # Save uploaded file
                filename = secure_filename(file.filename)
                unique_filename = f"{uuid.uuid4()}_{filename}"
                filepath = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
                file.save(filepath)
                
                # Make prediction
                results = model.predict(filepath, top_k=3)
                
                # Generate audio
                audio_file = audio_generator.generate_audio(results['predictions'][0]['class'])
                
                # Add additional info
                results['image_url'] = f"/static/uploads/{unique_filename}"
                results['audio_url'] = audio_file
                results['timestamp'] = datetime.now().isoformat()
                
                return jsonify(results)
        
        # Handle base64 image data (from camera)
        elif 'image_data' in request.json:
            image_data = request.json['image_data']
            
            # Decode base64 image
            image_data = image_data.split(',')[1]  # Remove data:image/jpeg;base64,
            image_bytes = base64.b64decode(image_data)
            
            # Save temporary file
            unique_filename = f"camera_{uuid.uuid4()}.jpg"
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
            
            with open(filepath, 'wb') as f:
                f.write(image_bytes)
            
            # Make prediction
            results = model.predict(filepath, top_k=3)
            
            # Generate audio
            audio_file = audio_generator.generate_audio(results['predictions'][0]['class'])
            
            # Add additional info
            results['image_url'] = f"/static/uploads/{unique_filename}"
            results['audio_url'] = audio_file
            results['timestamp'] = datetime.now().isoformat()
            
            return jsonify(results)
        
        return jsonify({'error': 'Invalid image format'}), 400
        
    except Exception as e:
        print(f"Error in prediction: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/model_info')
def model_info():
    """Get model information"""
    if model is None:
        return jsonify({'error': 'Model not loaded'}), 500
    
    return jsonify(model.get_model_info())

@app.route('/api/classes')
def get_classes():
    """Get list of supported classes"""
    if model is None:
        return jsonify({'error': 'Model not loaded'}), 500
    
    return jsonify({
        'classes': model.class_names,
        'count': len(model.class_names)
    })

@app.route('/static/uploads/<filename>')
def uploaded_file(filename):
    """Serve uploaded files"""
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

@app.route('/static/audio/<filename>')
def audio_file(filename):
    """Serve audio files"""
    return send_from_directory('static/audio', filename)

@app.errorhandler(404)
def not_found_error(error):
    """Handle 404 errors"""
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    return render_template('500.html'), 500

if __name__ == '__main__':
    # Create necessary directories
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    os.makedirs('static/audio', exist_ok=True)
    
    # Initialize app components
    if init_app():
        print("🚀 Starting SmartTrash Web Application...")
        print("📱 Open http://localhost:5000 in your browser")
        app.run(debug=True, host='0.0.0.0', port=5000)
    else:
        print("❌ Failed to initialize application")
        print("💡 Please ensure model files are in ../models/ directory")

{"cells": [{"cell_type": "markdown", "metadata": {"id": "smarttrash_title"}, "source": ["# 🗑️ SmartTrash Classification Model Training\n", "\n", "**<PERSON><PERSON><PERSON> t<PERSON>**: Training model ph<PERSON> loại rác thông minh với 10 categories\n", "\n", "**Dataset**: 10,138 ảnh với 10 classes:\n", "- battery, biological, brown-glass, cardboard, green-glass\n", "- metal, paper, plastic, trash, white-glass\n", "\n", "**Approach**: Transfer Learning với MobileNetV2"]}, {"cell_type": "markdown", "metadata": {"id": "setup_section"}, "source": ["## 📦 1. Setup Environment & Import Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install_packages"}, "outputs": [], "source": ["# Install required packages\n", "!pip install tensorflow==2.13.0\n", "!pip install matp<PERSON><PERSON>b seaborn\n", "!pip install pillow opencv-python\n", "!pip install scikit-learn"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "import_libraries"}, "outputs": [], "source": ["# Import libraries\n", "import tensorflow as tf\n", "from tensorflow import keras\n", "from tensorflow.keras import layers, models\n", "from tensorflow.keras.applications import MobileNetV2\n", "from tensorflow.keras.preprocessing.image import ImageDataGenerator\n", "from tensorflow.keras.optimizers import Adam\n", "from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint, ReduceLROnPlateau\n", "\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import os\n", "import cv2\n", "from PIL import Image\n", "import json\n", "from sklearn.metrics import classification_report, confusion_matrix\n", "import zipfile\n", "from google.colab import drive, files\n", "\n", "# Set random seeds for reproducibility\n", "np.random.seed(42)\n", "tf.random.set_seed(42)\n", "\n", "print(f\"TensorFlow version: {tf.__version__}\")\n", "print(f\"GPU available: {tf.config.list_physical_devices('GPU')}\")"]}, {"cell_type": "markdown", "metadata": {"id": "data_section"}, "source": ["## 💾 2. Mount Google Drive & Load Dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "mount_drive"}, "outputs": [], "source": ["# Mount Google Drive\n", "drive.mount('/content/drive')\n", "\n", "# Dataset paths (adjust these paths according to your Drive structure)\n", "DATASET_PATH = '/content/drive/MyDrive/SmartTrash/rubbish-data'\n", "MODEL_SAVE_PATH = '/content/drive/MyDrive/SmartTrash/models'\n", "\n", "# Create model save directory\n", "os.makedirs(MODEL_SAVE_PATH, exist_ok=True)\n", "\n", "# Class names\n", "CLASS_NAMES = ['battery', 'biological', 'brown-glass', 'cardboard', 'green-glass', \n", "               'metal', 'paper', 'plastic', 'trash', 'white-glass']\n", "\n", "print(f\"Dataset path: {DATASET_PATH}\")\n", "print(f\"Model save path: {MODEL_SAVE_PATH}\")\n", "print(f\"Number of classes: {len(CLASS_NAMES)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "verify_dataset"}, "outputs": [], "source": ["# Verify dataset structure\n", "def verify_dataset_structure(dataset_path):\n", "    \"\"\"Verify and display dataset structure\"\"\"\n", "    splits = ['train', 'val', 'test']\n", "    \n", "    for split in splits:\n", "        split_path = os.path.join(dataset_path, split)\n", "        if os.path.exists(split_path):\n", "            print(f\"\\n📁 {split.upper()} SET:\")\n", "            total_images = 0\n", "            \n", "            for class_name in CLASS_NAMES:\n", "                class_path = os.path.join(split_path, class_name)\n", "                if os.path.exists(class_path):\n", "                    count = len([f for f in os.listdir(class_path) \n", "                               if f.lower().endswith(('.jpg', '.jpeg', '.png'))])\n", "                    total_images += count\n", "                    print(f\"   {class_name:12}: {count:4d} images\")\n", "                else:\n", "                    print(f\"   {class_name:12}: NOT FOUND\")\n", "            \n", "            print(f\"   {'TOTAL':12}: {total_images:4d} images\")\n", "        else:\n", "            print(f\"\\n❌ {split.upper()} directory not found!\")\n", "\n", "verify_dataset_structure(DATASET_PATH)"]}, {"cell_type": "markdown", "metadata": {"id": "preprocessing_section"}, "source": ["## 🔄 3. Data Preprocessing & Augmentation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "data_generators"}, "outputs": [], "source": ["# Image parameters\n", "IMG_SIZE = 224\n", "BATCH_SIZE = 32\n", "NUM_CLASSES = len(CLASS_NAMES)\n", "\n", "# Data augmentation for training\n", "train_datagen = ImageDataGenerator(\n", "    rescale=1./255,\n", "    rotation_range=20,\n", "    width_shift_range=0.2,\n", "    height_shift_range=0.2,\n", "    shear_range=0.2,\n", "    zoom_range=0.2,\n", "    horizontal_flip=True,\n", "    brightness_range=[0.8, 1.2],\n", "    fill_mode='nearest'\n", ")\n", "\n", "# Only rescaling for validation and test\n", "val_test_datagen = ImageDataGenerator(rescale=1./255)\n", "\n", "# Create data generators\n", "train_generator = train_datagen.flow_from_directory(\n", "    os.path.join(DATASET_PATH, 'train'),\n", "    target_size=(IMG_SIZE, IMG_SIZE),\n", "    batch_size=BATCH_SIZE,\n", "    class_mode='categorical',\n", "    classes=CLASS_NAMES,\n", "    shuffle=True\n", ")\n", "\n", "val_generator = val_test_datagen.flow_from_directory(\n", "    os.path.join(DATASET_PATH, 'val'),\n", "    target_size=(IMG_SIZE, IMG_SIZE),\n", "    batch_size=BATCH_SIZE,\n", "    class_mode='categorical',\n", "    classes=CLASS_NAMES,\n", "    shuffle=False\n", ")\n", "\n", "test_generator = val_test_datagen.flow_from_directory(\n", "    os.path.join(DATASET_PATH, 'test'),\n", "    target_size=(IMG_SIZE, IMG_SIZE),\n", "    batch_size=BATCH_SIZE,\n", "    class_mode='categorical',\n", "    classes=CLASS_NAMES,\n", "    shuffle=False\n", ")\n", "\n", "print(f\"Training samples: {train_generator.samples}\")\n", "print(f\"Validation samples: {val_generator.samples}\")\n", "print(f\"Test samples: {test_generator.samples}\")\n", "print(f\"Class indices: {train_generator.class_indices}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "visualize_samples"}, "outputs": [], "source": ["# Visualize some training samples\n", "def plot_sample_images(generator, class_names, num_samples=8):\n", "    \"\"\"Plot sample images from the generator\"\"\"\n", "    plt.figure(figsize=(15, 8))\n", "    \n", "    # Get a batch of images\n", "    images, labels = next(generator)\n", "    \n", "    for i in range(min(num_samples, len(images))):\n", "        plt.subplot(2, 4, i + 1)\n", "        plt.imshow(images[i])\n", "        \n", "        # Get class name from one-hot encoded label\n", "        class_idx = np.argmax(labels[i])\n", "        class_name = class_names[class_idx]\n", "        \n", "        plt.title(f'{class_name}', fontsize=12)\n", "        plt.axis('off')\n", "    \n", "    plt.suptitle('Sample Training Images with Augmentation', fontsize=16)\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "plot_sample_images(train_generator, CLASS_NAMES)"]}, {"cell_type": "markdown", "metadata": {"id": "model_section"}, "source": ["## 🧠 4. Model Architecture (Transfer Learning)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "create_model"}, "outputs": [], "source": ["def create_model(num_classes, img_size=224):\n", "    \"\"\"Create transfer learning model with MobileNetV2\"\"\"\n", "    \n", "    # Load pre-trained MobileNetV2\n", "    base_model = MobileNetV2(\n", "        weights='imagenet',\n", "        include_top=False,\n", "        input_shape=(img_size, img_size, 3)\n", "    )\n", "    \n", "    # Freeze base model layers\n", "    base_model.trainable = False\n", "    \n", "    # Add custom classification head\n", "    model = models.Sequential([\n", "        base_model,\n", "        layers.GlobalAveragePooling2D(),\n", "        layers.Dropout(0.3),\n", "        layers.Dense(512, activation='relu'),\n", "        layers.BatchNormalization(),\n", "        layers.Dropout(0.5),\n", "        layers.Dense(256, activation='relu'),\n", "        layers.Dropout(0.3),\n", "        layers.Dense(num_classes, activation='softmax', name='predictions')\n", "    ])\n", "    \n", "    return model\n", "\n", "# Create model\n", "model = create_model(NUM_CLASSES, IMG_SIZE)\n", "\n", "# Compile model\n", "model.compile(\n", "    optimizer=<PERSON>(learning_rate=0.001),\n", "    loss='categorical_crossentropy',\n", "    metrics=['accuracy', 'top_3_accuracy']\n", ")\n", "\n", "# Model summary\n", "model.summary()"]}, {"cell_type": "markdown", "metadata": {"id": "training_section"}, "source": ["## 🏋️ 5. Model Training"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "setup_callbacks"}, "outputs": [], "source": ["# Setup callbacks\n", "callbacks = [\n", "    EarlyStopping(\n", "        monitor='val_accuracy',\n", "        patience=10,\n", "        restore_best_weights=True,\n", "        verbose=1\n", "    ),\n", "    ModelCheckpoint(\n", "        filepath=os.path.join(MODEL_SAVE_PATH, 'best_model.h5'),\n", "        monitor='val_accuracy',\n", "        save_best_only=True,\n", "        verbose=1\n", "    ),\n", "    ReduceLROnPlateau(\n", "        monitor='val_loss',\n", "        factor=0.5,\n", "        patience=5,\n", "        min_lr=1e-7,\n", "        verbose=1\n", "    )\n", "]\n", "\n", "print(\"Callbacks setup complete!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "train_model"}, "outputs": [], "source": ["# Training parameters\n", "EPOCHS = 50\n", "\n", "# Calculate steps per epoch\n", "steps_per_epoch = train_generator.samples // BATCH_SIZE\n", "validation_steps = val_generator.samples // BATCH_SIZE\n", "\n", "print(f\"Steps per epoch: {steps_per_epoch}\")\n", "print(f\"Validation steps: {validation_steps}\")\n", "print(f\"Starting training for {EPOCHS} epochs...\")\n", "\n", "# Train the model\n", "history = model.fit(\n", "    train_generator,\n", "    steps_per_epoch=steps_per_epoch,\n", "    epochs=EPOCHS,\n", "    validation_data=val_generator,\n", "    validation_steps=validation_steps,\n", "    callbacks=callbacks,\n", "    verbose=1\n", ")\n", "\n", "print(\"\\n✅ Training completed!\")"]}, {"cell_type": "markdown", "metadata": {"id": "evaluation_section"}, "source": ["## 📊 6. Model Evaluation & Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "plot_training_history"}, "outputs": [], "source": ["# Plot training history\n", "def plot_training_history(history):\n", "    \"\"\"Plot training and validation metrics\"\"\"\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "    \n", "    # Accuracy\n", "    axes[0,0].plot(history.history['accuracy'], label='Training Accuracy')\n", "    axes[0,0].plot(history.history['val_accuracy'], label='Validation Accuracy')\n", "    axes[0,0].set_title('Model Accuracy')\n", "    axes[0,0].set_xlabel('Epoch')\n", "    axes[0,0].set_ylabel('Accuracy')\n", "    axes[0,0].legend()\n", "    axes[0,0].grid(True)\n", "    \n", "    # Loss\n", "    axes[0,1].plot(history.history['loss'], label='Training Loss')\n", "    axes[0,1].plot(history.history['val_loss'], label='Validation Loss')\n", "    axes[0,1].set_title('Model Loss')\n", "    axes[0,1].set_xlabel('Epoch')\n", "    axes[0,1].set_ylabel('Loss')\n", "    axes[0,1].legend()\n", "    axes[0,1].grid(True)\n", "    \n", "    # Top-3 Accuracy\n", "    if 'top_3_accuracy' in history.history:\n", "        axes[1,0].plot(history.history['top_3_accuracy'], label='Training Top-3 Acc')\n", "        axes[1,0].plot(history.history['val_top_3_accuracy'], label='Validation Top-3 Acc')\n", "        axes[1,0].set_title('Top-3 Accuracy')\n", "        axes[1,0].set_xlabel('Epoch')\n", "        axes[1,0].set_ylabel('Top-3 Accuracy')\n", "        axes[1,0].legend()\n", "        axes[1,0].grid(True)\n", "    \n", "    # Learning Rate\n", "    if 'lr' in history.history:\n", "        axes[1,1].plot(history.history['lr'], label='Learning Rate')\n", "        axes[1,1].set_title('Learning Rate Schedule')\n", "        axes[1,1].set_xlabel('Epoch')\n", "        axes[1,1].set_ylabel('Learning Rate')\n", "        axes[1,1].set_yscale('log')\n", "        axes[1,1].legend()\n", "        axes[1,1].grid(True)\n", "    \n", "    plt.tight_layout()\n", "    plt.savefig(os.path.join(MODEL_SAVE_PATH, 'training_history.png'), dpi=300, bbox_inches='tight')\n", "    plt.show()\n", "\n", "plot_training_history(history)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "evaluate_model"}, "outputs": [], "source": ["# Evaluate on test set\n", "print(\"🔍 Evaluating model on test set...\")\n", "test_loss, test_accuracy, test_top3_accuracy = model.evaluate(test_generator, verbose=1)\n", "\n", "print(f\"\\n📊 TEST RESULTS:\")\n", "print(f\"   • Test Loss: {test_loss:.4f}\")\n", "print(f\"   • Test Accuracy: {test_accuracy:.4f} ({test_accuracy*100:.2f}%)\")\n", "print(f\"   • Test Top-3 Accuracy: {test_top3_accuracy:.4f} ({test_top3_accuracy*100:.2f}%)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "confusion_matrix"}, "outputs": [], "source": ["# Generate predictions for confusion matrix\n", "print(\"🔮 Generating predictions...\")\n", "test_generator.reset()\n", "predictions = model.predict(test_generator, verbose=1)\n", "predicted_classes = np.argmax(predictions, axis=1)\n", "\n", "# Get true labels\n", "true_classes = test_generator.classes\n", "\n", "# Classification report\n", "print(\"\\n📋 CLASSIFICATION REPORT:\")\n", "print(classification_report(true_classes, predicted_classes, target_names=CLASS_NAMES))\n", "\n", "# Confusion Matrix\n", "cm = confusion_matrix(true_classes, predicted_classes)\n", "\n", "plt.figure(figsize=(12, 10))\n", "sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', \n", "            xticklabels=CLASS_NAMES, yticklabels=CLASS_NAMES)\n", "plt.title('Confusion Matrix')\n", "plt.xlabel('Predicted Label')\n", "plt.ylabel('True Label')\n", "plt.xticks(rotation=45)\n", "plt.yticks(rotation=0)\n", "plt.tight_layout()\n", "plt.savefig(os.path.join(MODEL_SAVE_PATH, 'confusion_matrix.png'), dpi=300, bbox_inches='tight')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"id": "finetune_section"}, "source": ["## 🔧 7. Fine-tuning (Optional)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "fine_tuning"}, "outputs": [], "source": ["# Fine-tuning: Unfreeze some layers of the base model\n", "def fine_tune_model(model, learning_rate=1e-5):\n", "    \"\"\"Fine-tune the model by unfreezing some layers\"\"\"\n", "    \n", "    # Unfreeze the top layers of the base model\n", "    base_model = model.layers[0]\n", "    base_model.trainable = True\n", "    \n", "    # Fine-tune from this layer onwards\n", "    fine_tune_at = 100\n", "    \n", "    # Freeze all the layers before the `fine_tune_at` layer\n", "    for layer in base_model.layers[:fine_tune_at]:\n", "        layer.trainable = False\n", "    \n", "    # Recompile with lower learning rate\n", "    model.compile(\n", "        optimizer=Adam(learning_rate=learning_rate),\n", "        loss='categorical_crossentropy',\n", "        metrics=['accuracy', 'top_3_accuracy']\n", "    )\n", "    \n", "    return model\n", "\n", "# Uncomment to perform fine-tuning\n", "# print(\"🔧 Starting fine-tuning...\")\n", "# model = fine_tune_model(model, learning_rate=1e-5)\n", "\n", "# # Fine-tuning callbacks\n", "# finetune_callbacks = [\n", "#     EarlyStopping(monitor='val_accuracy', patience=5, restore_best_weights=True),\n", "#     ModelCheckpoint(\n", "#         filepath=os.path.join(MODEL_SAVE_PATH, 'finetuned_model.h5'),\n", "#         monitor='val_accuracy',\n", "#         save_best_only=True\n", "#     )\n", "# ]\n", "\n", "# # Fine-tune for fewer epochs\n", "# history_finetune = model.fit(\n", "#     train_generator,\n", "#     steps_per_epoch=steps_per_epoch,\n", "#     epochs=10,\n", "#     validation_data=val_generator,\n", "#     validation_steps=validation_steps,\n", "#     callbacks=finetune_callbacks,\n", "#     verbose=1\n", "# )\n", "\n", "print(\"Fine-tuning section ready (uncomment to use)\")"]}, {"cell_type": "markdown", "metadata": {"id": "export_section"}, "source": ["## 💾 8. Export Model for Production"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "save_model"}, "outputs": [], "source": ["# Save the final model\n", "final_model_path = os.path.join(MODEL_SAVE_PATH, 'smarttrash_model.h5')\n", "model.save(final_model_path)\n", "print(f\"✅ Model saved to: {final_model_path}\")\n", "\n", "# Save class labels\n", "labels_path = os.path.join(MODEL_SAVE_PATH, 'labels.txt')\n", "with open(labels_path, 'w') as f:\n", "    for label in CLASS_NAMES:\n", "        f.write(f\"{label}\\n\")\n", "print(f\"✅ Labels saved to: {labels_path}\")\n", "\n", "# Save model info\n", "model_info = {\n", "    'model_name': 'SmartTrash Classifier',\n", "    'architecture': 'MobileNetV2 + Custom Head',\n", "    'input_size': [IMG_SIZE, IMG_SIZE, 3],\n", "    'num_classes': NUM_CLASSES,\n", "    'class_names': CLASS_NAMES,\n", "    'test_accuracy': float(test_accuracy),\n", "    'test_top3_accuracy': float(test_top3_accuracy),\n", "    'preprocessing': {\n", "        'rescale': '1./255',\n", "        'target_size': [IMG_SIZE, IMG_SIZE]\n", "    },\n", "    'training_info': {\n", "        'batch_size': BATCH_SIZE,\n", "        'epochs_trained': len(history.history['accuracy']),\n", "        'optimizer': '<PERSON>',\n", "        'loss': 'categorical_crossentropy'\n", "    }\n", "}\n", "\n", "info_path = os.path.join(MODEL_SAVE_PATH, 'model_info.json')\n", "with open(info_path, 'w') as f:\n", "    json.dump(model_info, f, indent=2)\n", "print(f\"✅ Model info saved to: {info_path}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "convert_tflite"}, "outputs": [], "source": ["# Convert to TensorFlow Lite for mobile deployment (optional)\n", "def convert_to_tflite(model, save_path):\n", "    \"\"\"Convert model to TensorFlow Lite format\"\"\"\n", "    converter = tf.lite.TFLiteConverter.from_keras_model(model)\n", "    converter.optimizations = [tf.lite.Optimize.DEFAULT]\n", "    \n", "    # Convert the model\n", "    tflite_model = converter.convert()\n", "    \n", "    # Save the model\n", "    tflite_path = os.path.join(save_path, 'smarttrash_model.tflite')\n", "    with open(tflite_path, 'wb') as f:\n", "        f.write(tflite_model)\n", "    \n", "    print(f\"✅ TFLite model saved to: {tflite_path}\")\n", "    return tflite_path\n", "\n", "# Uncomment to convert to TFLite\n", "# tflite_path = convert_to_tflite(model, MODEL_SAVE_PATH)\n", "\n", "print(\"TFLite conversion ready (uncomment to use)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "test_prediction"}, "outputs": [], "source": ["# Test prediction function\n", "def predict_image(model, image_path, class_names, img_size=224):\n", "    \"\"\"Predict single image\"\"\"\n", "    # Load and preprocess image\n", "    img = tf.keras.preprocessing.image.load_img(image_path, target_size=(img_size, img_size))\n", "    img_array = tf.keras.preprocessing.image.img_to_array(img)\n", "    img_array = np.expand_dims(img_array, axis=0) / 255.0\n", "    \n", "    # Make prediction\n", "    predictions = model.predict(img_array, verbose=0)\n", "    predicted_class_idx = np.argmax(predictions[0])\n", "    confidence = predictions[0][predicted_class_idx]\n", "    \n", "    return class_names[predicted_class_idx], confidence\n", "\n", "# Test with a sample image\n", "sample_image_path = os.path.join(DATASET_PATH, 'test', 'plastic', 'plastic1.jpg')\n", "if os.path.exists(sample_image_path):\n", "    predicted_class, confidence = predict_image(model, sample_image_path, CLASS_NAMES)\n", "    print(f\"\\n🔮 SAMPLE PREDICTION:\")\n", "    print(f\"   Image: {sample_image_path}\")\n", "    print(f\"   Predicted: {predicted_class}\")\n", "    print(f\"   Confidence: {confidence:.4f} ({confidence*100:.2f}%)\")\n", "else:\n", "    print(\"Sample image not found for testing\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "download_files"}, "outputs": [], "source": ["# Download model files to local machine\n", "print(\"📥 Downloading model files...\")\n", "\n", "# Create a zip file with all model files\n", "zip_path = os.path.join(MODEL_SAVE_PATH, 'smarttrash_model_package.zip')\n", "with zipfile.ZipFile(zip_path, 'w') as zipf:\n", "    # Add model file\n", "    zipf.write(final_model_path, 'smarttrash_model.h5')\n", "    # Add labels\n", "    zipf.write(labels_path, 'labels.txt')\n", "    # Add model info\n", "    zipf.write(info_path, 'model_info.json')\n", "    # Add training plots if they exist\n", "    plots = ['training_history.png', 'confusion_matrix.png']\n", "    for plot in plots:\n", "        plot_path = os.path.join(MODEL_SAVE_PATH, plot)\n", "        if os.path.exists(plot_path):\n", "            zipf.write(plot_path, plot)\n", "\n", "print(f\"✅ Model package created: {zip_path}\")\n", "\n", "# Download the zip file\n", "files.download(zip_path)\n", "print(\"📦 Model package downloaded!\")"]}, {"cell_type": "markdown", "metadata": {"id": "summary_section"}, "source": ["## 📋 9. Training Summary\n", "\n", "### 🎯 Model Performance\n", "- **Architecture**: MobileNetV2 with Transfer Learning\n", "- **Dataset**: 10,138 images across 10 waste categories\n", "- **Input Size**: 224x224x3\n", "- **Training Strategy**: Transfer Learning + Data Augmentation\n", "\n", "### 📊 Results\n", "- Test Accuracy: Will be displayed after training\n", "- Top-3 Accuracy: Will be displayed after training\n", "\n", "### 📁 Output Files\n", "1. `smarttrash_model.h5` - Trained Keras model\n", "2. `labels.txt` - Class labels\n", "3. `model_info.json` - Model metadata\n", "4. `training_history.png` - Training plots\n", "5. `confusion_matrix.png` - Confusion matrix\n", "\n", "### 🚀 Next Steps\n", "1. Download the model package\n", "2. Integrate into web application\n", "3. Test with real-world images\n", "4. Deploy to production\n", "\n", "### 💡 Usage in Web App\n", "```python\n", "# Load model\n", "model = tf.keras.models.load_model('smarttrash_model.h5')\n", "\n", "# Preprocess image\n", "img = tf.keras.preprocessing.image.load_img(image_path, target_size=(224, 224))\n", "img_array = tf.keras.preprocessing.image.img_to_array(img) / 255.0\n", "img_array = np.expand_dims(img_array, axis=0)\n", "\n", "# Predict\n", "predictions = model.predict(img_array)\n", "predicted_class = class_names[np.argmax(predictions)]\n", "```"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}